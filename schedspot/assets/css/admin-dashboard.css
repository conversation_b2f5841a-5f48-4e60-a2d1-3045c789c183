/**
 * SchedSpot Admin Dashboard Styles
 *
 * @package SchedSpot
 * @version 1.0.0
 */

/* Dashboard Widgets */
.schedspot-dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.schedspot-widget {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.schedspot-widget h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

/* Statistics Grid */
.schedspot-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Quick Actions */
.schedspot-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Placeholder Styles */
.schedspot-placeholder {
    text-align: center;
    padding: 40px;
    background: #f9f9f9;
    border-radius: 4px;
    margin-top: 20px;
}

/* Role Switcher */
.schedspot-role-switcher {
    max-width: 800px;
}

.current-role-info {
    background: #f1f1f1;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.role-descriptions {
    margin-top: 30px;
}

.role-description {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.role-description h4 {
    margin-top: 0;
    color: #0073aa;
}

.role-capabilities {
    margin-top: 10px;
}

.role-capabilities ul {
    margin: 5px 0 0 20px;
}

.role-capabilities li {
    margin-bottom: 3px;
}

.switch-role-form {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
}

.switch-role-form h3 {
    margin-top: 0;
}

.role-selection {
    margin: 15px 0;
}

.role-selection label {
    display: block;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.role-selection label:hover {
    background-color: #f9f9f9;
}

.role-selection input[type="radio"] {
    margin-right: 10px;
}

.role-selection .role-name {
    font-weight: bold;
}

.role-selection .role-desc {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Booking Information Grid */
.booking-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.booking-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
}

.booking-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #0073aa;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.booking-details {
    display: grid;
    gap: 10px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: bold;
    color: #333;
}

.detail-value {
    color: #666;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-completed {
    background: #cce5ff;
    color: #004085;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Booking Messages */
.booking-messages {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
}

.message-item {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
}

.worker-message {
    background: #e3f2fd;
    margin-left: 20px;
}

.customer-message {
    background: #f3e5f5;
    margin-right: 20px;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
}

.message-header strong {
    color: #0073aa;
}

.message-time {
    color: #666;
}

.message-content {
    line-height: 1.4;
}

/* Booking Timeline */
.booking-timeline {
    position: relative;
    padding-left: 30px;
}

.booking-timeline::before {
    content: "";
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ddd;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-marker.status-pending {
    background: #ffc107;
}

.timeline-marker.status-confirmed {
    background: #28a745;
}

.timeline-marker.status-completed {
    background: #007bff;
}

.timeline-marker.status-cancelled {
    background: #dc3545;
}

.timeline-content {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.timeline-header strong {
    color: #0073aa;
}

.timeline-date {
    color: #666;
    font-size: 12px;
}

.timeline-description {
    color: #333;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedspot-dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .schedspot-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .booking-info-grid {
        grid-template-columns: 1fr;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 5px;
    }
    
    .timeline-header {
        flex-direction: column;
        gap: 5px;
    }
}
