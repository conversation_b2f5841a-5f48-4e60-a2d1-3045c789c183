<?php
/**
 * Admin Role Switcher Template
 *
 * @package SchedSpot
 * @version 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e( 'Admin Role Switcher', 'schedspot' ); ?></h1>
    
    <?php if ( isset( $_GET['switched'] ) ) : ?>
        <div class="notice notice-success is-dismissible">
            <p><?php _e( 'Role switched successfully!', 'schedspot' ); ?></p>
        </div>
    <?php endif; ?>

    <div class="schedspot-role-switcher">
        <div class="current-role-info">
            <h2><?php _e( 'Current Mode', 'schedspot' ); ?></h2>
            <p><strong><?php printf( __( 'You are currently viewing SchedSpot as: %s', 'schedspot' ), $this->get_role_display_name( $current_role ) ); ?></strong></p>
            <p><?php echo esc_html( $this->get_role_description( $current_role ) ); ?></p>
        </div>

        <div class="switch-role-form">
            <h3><?php _e( 'Switch Role Mode', 'schedspot' ); ?></h3>
            <p><?php _e( 'Switch between different user role perspectives to test the plugin functionality from different user viewpoints.', 'schedspot' ); ?></p>

            <form method="post" action="">
                <?php wp_nonce_field( 'schedspot_switch_role' ); ?>
                
                <div class="role-selection">
                    <label>
                        <input type="radio" name="target_role" value="administrator" <?php checked( $current_role, 'administrator' ); ?>>
                        <span class="role-name"><?php _e( 'Administrator', 'schedspot' ); ?></span>
                        <span class="role-desc"><?php _e( 'Full administrative access to all SchedSpot features', 'schedspot' ); ?></span>
                    </label>

                    <label>
                        <input type="radio" name="target_role" value="schedspot_worker" <?php checked( $current_role, 'schedspot_worker' ); ?>>
                        <span class="role-name"><?php _e( 'Worker', 'schedspot' ); ?></span>
                        <span class="role-desc"><?php _e( 'Service provider experience - manage bookings, availability, and earnings', 'schedspot' ); ?></span>
                    </label>

                    <label>
                        <input type="radio" name="target_role" value="schedspot_customer" <?php checked( $current_role, 'schedspot_customer' ); ?>>
                        <span class="role-name"><?php _e( 'Customer', 'schedspot' ); ?></span>
                        <span class="role-desc"><?php _e( 'Client booking experience - book services, manage appointments, and communicate with workers', 'schedspot' ); ?></span>
                    </label>
                </div>

                <div class="test-user-selection" style="margin-top: 20px;">
                    <h4><?php _e( 'Test User (Optional)', 'schedspot' ); ?></h4>
                    <p class="description"><?php _e( 'Select a specific user to test with when switching to Worker or Customer mode.', 'schedspot' ); ?></p>
                    
                    <select name="test_user_id" id="test_user_id">
                        <option value=""><?php _e( 'Use current admin user', 'schedspot' ); ?></option>
                        <?php
                        $workers = get_users( array( 'role' => 'schedspot_worker' ) );
                        if ( ! empty( $workers ) ) :
                        ?>
                            <optgroup label="<?php esc_attr_e( 'Workers', 'schedspot' ); ?>">
                                <?php foreach ( $workers as $worker ) : ?>
                                    <option value="<?php echo esc_attr( $worker->ID ); ?>">
                                        <?php echo esc_html( $worker->display_name . ' (' . $worker->user_email . ')' ); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endif; ?>

                        <?php
                        $customers = get_users( array( 'role' => 'schedspot_customer' ) );
                        if ( ! empty( $customers ) ) :
                        ?>
                            <optgroup label="<?php esc_attr_e( 'Customers', 'schedspot' ); ?>">
                                <?php foreach ( $customers as $customer ) : ?>
                                    <option value="<?php echo esc_attr( $customer->ID ); ?>">
                                        <?php echo esc_html( $customer->display_name . ' (' . $customer->user_email . ')' ); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endif; ?>
                    </select>
                </div>

                <p class="submit">
                    <input type="submit" name="switch_role" class="button button-primary" value="<?php esc_attr_e( 'Switch Role Mode', 'schedspot' ); ?>">
                </p>
            </form>
        </div>

        <div class="role-descriptions">
            <h3><?php _e( 'Role Descriptions', 'schedspot' ); ?></h3>
            
            <div class="role-description">
                <h4><?php _e( 'Administrator', 'schedspot' ); ?></h4>
                <p><?php _e( 'Full access to all SchedSpot features including:', 'schedspot' ); ?></p>
                <div class="role-capabilities">
                    <ul>
                        <li><?php _e( 'Manage all bookings, workers, and services', 'schedspot' ); ?></li>
                        <li><?php _e( 'Configure plugin settings and integrations', 'schedspot' ); ?></li>
                        <li><?php _e( 'View analytics and reports', 'schedspot' ); ?></li>
                        <li><?php _e( 'Handle payments and commissions', 'schedspot' ); ?></li>
                        <li><?php _e( 'Manage user roles and permissions', 'schedspot' ); ?></li>
                    </ul>
                </div>
            </div>

            <div class="role-description">
                <h4><?php _e( 'Worker', 'schedspot' ); ?></h4>
                <p><?php _e( 'Service provider interface with access to:', 'schedspot' ); ?></p>
                <div class="role-capabilities">
                    <ul>
                        <li><?php _e( 'Manage personal availability and schedule', 'schedspot' ); ?></li>
                        <li><?php _e( 'View and respond to booking requests', 'schedspot' ); ?></li>
                        <li><?php _e( 'Communicate with clients through messaging', 'schedspot' ); ?></li>
                        <li><?php _e( 'Track earnings and payment history', 'schedspot' ); ?></li>
                        <li><?php _e( 'Update profile and service offerings', 'schedspot' ); ?></li>
                        <li><?php _e( 'Set custom pricing for services', 'schedspot' ); ?></li>
                    </ul>
                </div>
            </div>

            <div class="role-description">
                <h4><?php _e( 'Customer', 'schedspot' ); ?></h4>
                <p><?php _e( 'Client booking interface with access to:', 'schedspot' ); ?></p>
                <div class="role-capabilities">
                    <ul>
                        <li><?php _e( 'Browse and book available services', 'schedspot' ); ?></li>
                        <li><?php _e( 'View booking history and status', 'schedspot' ); ?></li>
                        <li><?php _e( 'Communicate with assigned workers', 'schedspot' ); ?></li>
                        <li><?php _e( 'Make payments and view invoices', 'schedspot' ); ?></li>
                        <li><?php _e( 'Rate and review completed services', 'schedspot' ); ?></li>
                        <li><?php _e( 'Manage personal profile and preferences', 'schedspot' ); ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="quick-links" style="margin-top: 30px; padding: 20px; background: #f9f9f9; border-radius: 5px;">
            <h3><?php _e( 'Quick Links', 'schedspot' ); ?></h3>
            <p><?php _e( 'After switching roles, you can access the frontend interfaces:', 'schedspot' ); ?></p>
            <ul>
                <li><a href="<?php echo home_url( '/?schedspot_action=booking_form' ); ?>" target="_blank"><?php _e( 'Booking Form', 'schedspot' ); ?></a></li>
                <li><a href="<?php echo home_url( '/?schedspot_action=dashboard' ); ?>" target="_blank"><?php _e( 'User Dashboard', 'schedspot' ); ?></a></li>
                <li><a href="<?php echo home_url( '/?schedspot_action=messages' ); ?>" target="_blank"><?php _e( 'Messages', 'schedspot' ); ?></a></li>
                <li><a href="<?php echo home_url( '/?schedspot_action=profile' ); ?>" target="_blank"><?php _e( 'Profile Settings', 'schedspot' ); ?></a></li>
            </ul>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Show/hide test user selection based on role
    $('input[name="target_role"]').change(function() {
        var role = $(this).val();
        if (role === 'administrator') {
            $('.test-user-selection').hide();
        } else {
            $('.test-user-selection').show();
        }
    });

    // Trigger change event on page load
    $('input[name="target_role"]:checked').trigger('change');
});
</script>
